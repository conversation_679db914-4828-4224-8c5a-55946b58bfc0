#!/usr/bin/env python3
"""
专门检查通达信数据源连接和数据获取状态
"""

import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

def check_tdx_library():
    """检查通达信库安装状态"""
    print("🔍 检查通达信库安装状态...")
    try:
        import pytdx
        from pytdx.hq import TdxHq_API
        from pytdx.exhq import TdxExHq_API
        print(f"✅ pytdx库版本: {pytdx.__version__ if hasattr(pytdx, '__version__') else '未知版本'}")
        print("✅ TdxHq_API 导入成功")
        print("✅ TdxExHq_API 导入成功")
        return True
    except ImportError as e:
        print(f"❌ pytdx库导入失败: {e}")
        print("💡 安装命令: pip install pytdx")
        return False

def check_tdx_provider():
    """检查通达信数据提供器"""
    print("\n🔍 检查通达信数据提供器...")
    try:
        from tradingagents.dataflows.tdx_utils import TongDaXinDataProvider
        
        provider = TongDaXinDataProvider()
        print("✅ TongDaXinDataProvider 创建成功")
        return provider
    except Exception as e:
        print(f"❌ TongDaXinDataProvider 创建失败: {e}")
        return None

def test_tdx_connection(provider):
    """测试通达信连接"""
    print("\n🔍 测试通达信服务器连接...")
    
    if not provider:
        print("❌ 无法测试连接，提供器未创建")
        return False
    
    try:
        success = provider.connect()
        if success:
            print("✅ 通达信服务器连接成功")
            print(f"✅ 连接状态: {provider.is_connected()}")
            return True
        else:
            print("❌ 通达信服务器连接失败")
            return False
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        return False

def test_realtime_data(provider):
    """测试实时数据获取"""
    print("\n🔍 测试实时数据获取...")
    
    if not provider or not provider.is_connected():
        print("❌ 无法测试实时数据，连接未建立")
        return
    
    test_stocks = ["000001", "600036", "000858"]  # 平安银行、招商银行、五粮液
    
    for stock_code in test_stocks:
        try:
            print(f"📊 获取 {stock_code} 实时数据...")
            data = provider.get_stock_realtime_data(stock_code)
            
            if data:
                print(f"✅ {stock_code} 实时数据获取成功:")
                print(f"   股票名称: {data.get('name', 'N/A')}")
                print(f"   当前价格: ¥{data.get('price', 'N/A')}")
                print(f"   涨跌幅: {data.get('change_percent', 'N/A')}%")
                print(f"   成交量: {data.get('volume', 'N/A')}")
            else:
                print(f"❌ {stock_code} 实时数据获取失败")
                
        except Exception as e:
            print(f"❌ {stock_code} 实时数据获取异常: {e}")

def test_historical_data(provider):
    """测试历史数据获取"""
    print("\n🔍 测试历史数据获取...")
    
    if not provider or not provider.is_connected():
        print("❌ 无法测试历史数据，连接未建立")
        return
    
    stock_code = "600036"  # 招商银行
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d')
    
    try:
        print(f"📊 获取 {stock_code} 历史数据 ({start_date} 到 {end_date})...")
        df = provider.get_stock_history_data(stock_code, start_date, end_date)
        
        if not df.empty:
            print(f"✅ {stock_code} 历史数据获取成功:")
            print(f"   数据条数: {len(df)}")
            print(f"   数据列: {list(df.columns)}")
            print(f"   最新收盘价: ¥{df.iloc[-1]['close']:.2f}")
            print(f"   期间最高: ¥{df['high'].max():.2f}")
            print(f"   期间最低: ¥{df['low'].min():.2f}")
            
            # 显示最近3天数据
            print("\n📈 最近3天数据:")
            recent_data = df.tail(3)[['open', 'high', 'low', 'close', 'volume']]
            print(recent_data.to_string())
        else:
            print(f"❌ {stock_code} 历史数据获取失败 - 数据为空")
            
    except Exception as e:
        print(f"❌ {stock_code} 历史数据获取异常: {e}")

def test_technical_indicators(provider):
    """测试技术指标计算"""
    print("\n🔍 测试技术指标计算...")
    
    if not provider or not provider.is_connected():
        print("❌ 无法测试技术指标，连接未建立")
        return
    
    stock_code = "600036"  # 招商银行
    
    try:
        print(f"📊 计算 {stock_code} 技术指标...")
        indicators = provider.get_stock_technical_indicators(stock_code)
        
        if indicators:
            print(f"✅ {stock_code} 技术指标计算成功:")
            for key, value in indicators.items():
                if isinstance(value, (int, float)):
                    print(f"   {key}: {value:.2f}")
                else:
                    print(f"   {key}: {value}")
        else:
            print(f"❌ {stock_code} 技术指标计算失败")
            
    except Exception as e:
        print(f"❌ {stock_code} 技术指标计算异常: {e}")

def test_market_overview(provider):
    """测试市场概览"""
    print("\n🔍 测试市场概览获取...")
    
    if not provider or not provider.is_connected():
        print("❌ 无法测试市场概览，连接未建立")
        return
    
    try:
        print("📊 获取市场概览...")
        market_data = provider.get_market_overview()
        
        if market_data:
            print("✅ 市场概览获取成功:")
            for market_name, data in market_data.items():
                print(f"   {market_name}:")
                print(f"     点位: {data.get('price', 'N/A')}")
                print(f"     涨跌: {data.get('change', 'N/A')}")
                print(f"     涨跌幅: {data.get('change_percent', 'N/A')}%")
        else:
            print("❌ 市场概览获取失败")
            
    except Exception as e:
        print(f"❌ 市场概览获取异常: {e}")

def test_integrated_function():
    """测试集成函数"""
    print("\n🔍 测试集成数据获取函数...")
    
    try:
        from tradingagents.dataflows.tdx_utils import get_china_stock_data
        
        stock_code = "000001"  # 平安银行
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d')
        
        print(f"📊 使用集成函数获取 {stock_code} 数据...")
        result = get_china_stock_data(stock_code, start_date, end_date)
        
        if result and "❌" not in result:
            print("✅ 集成函数数据获取成功")
            print("📈 数据预览 (前300字符):")
            print("-" * 50)
            print(result[:300] + "..." if len(result) > 300 else result)
            print("-" * 50)
        else:
            print("❌ 集成函数数据获取失败")
            if result:
                print("错误信息:")
                print(result[:500])
                
    except Exception as e:
        print(f"❌ 集成函数测试异常: {e}")

def main():
    print("=" * 70)
    print("🔍 TradingAgents-CN 通达信数据源连接检查")
    print("=" * 70)
    
    # 1. 检查库安装
    if not check_tdx_library():
        print("\n❌ 通达信库未正确安装，无法继续测试")
        return
    
    # 2. 创建提供器
    provider = check_tdx_provider()
    
    # 3. 测试连接
    connected = test_tdx_connection(provider)
    
    if connected:
        # 4. 测试各种数据获取功能
        test_realtime_data(provider)
        test_historical_data(provider)
        test_technical_indicators(provider)
        test_market_overview(provider)
        
        # 5. 断开连接
        provider.disconnect()
        print("\n✅ 通达信连接已断开")
    
    # 6. 测试集成函数
    test_integrated_function()
    
    print("\n" + "=" * 70)
    print("🎯 通达信数据源检查完成")
    print("=" * 70)
    
    if connected:
        print("✅ 通达信数据源工作正常，可以正常获取中国股票数据")
    else:
        print("❌ 通达信数据源存在问题，请检查网络连接或服务器状态")

if __name__ == "__main__":
    main()
