#!/usr/bin/env python3
"""
测试TradingAgents-CN数据源配置
"""

import sys
import os

def test_imports():
    """测试必要的库导入"""
    print("🔍 测试数据源库导入...")
    
    try:
        import pytdx
        print("✅ pytdx (通达信API) - 已安装")
    except ImportError:
        print("❌ pytdx (通达信API) - 未安装")
        print("   安装命令: pip install pytdx")
    
    try:
        import yfinance
        print("✅ yfinance (Yahoo Finance) - 已安装")
    except ImportError:
        print("❌ yfinance (Yahoo Finance) - 未安装")
        print("   安装命令: pip install yfinance")
    
    try:
        import finnhub
        print("✅ finnhub (FinnHub API) - 已安装")
    except ImportError:
        print("❌ finnhub (FinnHub API) - 未安装")
        print("   安装命令: pip install finnhub-python")
    
    try:
        import pandas as pd
        print("✅ pandas - 已安装")
    except ImportError:
        print("❌ pandas - 未安装")

def test_tradingagents_imports():
    """测试TradingAgents模块导入"""
    print("\n🔍 测试TradingAgents模块导入...")
    
    try:
        from tradingagents.dataflows.tdx_utils import get_china_stock_data
        print("✅ 通达信数据模块 - 可用")
    except ImportError as e:
        print(f"❌ 通达信数据模块 - 导入失败: {e}")
    
    try:
        from tradingagents.dataflows.interface import get_YFin_data_online
        print("✅ Yahoo Finance数据模块 - 可用")
    except ImportError as e:
        print(f"❌ Yahoo Finance数据模块 - 导入失败: {e}")
    
    try:
        from tradingagents.dataflows.chinese_finance_utils import get_chinese_social_sentiment
        print("✅ 中文财经数据模块 - 可用")
    except ImportError as e:
        print(f"❌ 中文财经数据模块 - 导入失败: {e}")

def test_tdx_connection():
    """测试通达信连接"""
    print("\n🔍 测试通达信API连接...")
    
    try:
        from tradingagents.dataflows.tdx_utils import TongDaXinDataProvider
        
        provider = TongDaXinDataProvider()
        success = provider.connect()
        
        if success:
            print("✅ 通达信API连接成功")
            provider.disconnect()
        else:
            print("❌ 通达信API连接失败")
            print("   可能原因: 网络连接问题或服务器不可用")
            
    except Exception as e:
        print(f"❌ 通达信API测试失败: {e}")

def check_config_files():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    config_files = [
        "config/settings.json",
        "config/models.json", 
        "config/pricing.json",
        ".env"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file} - 存在")
        else:
            print(f"⚠️ {config_file} - 不存在")

def main():
    print("=" * 60)
    print("🚀 TradingAgents-CN 数据源配置测试")
    print("=" * 60)
    
    test_imports()
    test_tradingagents_imports()
    test_tdx_connection()
    check_config_files()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
