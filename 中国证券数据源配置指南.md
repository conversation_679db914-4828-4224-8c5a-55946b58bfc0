# 🇨🇳 TradingAgents-CN 中国证券市场数据源配置指南

## 📊 配置状态总览

根据测试结果，您的WSL环境已经完成了基础配置：

✅ **通达信API** - 主要中国股票数据源，连接正常  
✅ **Yahoo Finance** - 备用数据源，支持港股和美股  
✅ **FinnHub API** - 国际市场数据  
✅ **中文财经数据模块** - 新闻和情绪分析  
✅ **配置文件** - 所有必要配置文件已存在  

## 🎯 数据源详细配置

### 1. 通达信API（主要A股数据源）

**功能特点：**
- 📈 实时A股、深股、创业板、科创板数据
- 🔄 历史K线数据（日线、周线、月线）
- 📊 技术指标计算
- 🆓 完全免费，无需API密钥

**支持的股票代码格式：**
```python
# A股示例
"000001"  # 平安银行（深交所）
"600036"  # 招商银行（上交所）
"300015"  # 爱尔眼科（创业板）
"688981"  # 中芯国际（科创板）
```

**使用示例：**
```python
from tradingagents.dataflows.tdx_utils import get_china_stock_data

# 获取招商银行最近30天数据
data = get_china_stock_data("600036", "2024-06-01", "2024-06-30")
print(data)
```

### 2. Yahoo Finance（港股和国际市场）

**功能特点：**
- 🌏 港股、美股、国际市场数据
- 📈 历史数据和实时行情
- 💰 财务数据和基本面信息

**支持的股票代码格式：**
```python
# 港股示例
"0700.HK"   # 腾讯控股
"9988.HK"   # 阿里巴巴
"1810.HK"   # 小米集团

# 美股示例
"AAPL"      # 苹果
"TSLA"      # 特斯拉
"BABA"      # 阿里巴巴ADR
```

### 3. 中文财经数据聚合器

**功能特点：**
- 📰 中文财经新闻情绪分析
- 📊 社交媒体情绪监控
- 🔍 综合市场情绪评估

## 🔧 高级配置选项

### 1. 数据源优先级配置

编辑 `tradingagents/dataflows/config.py`：

```python
# 数据源优先级设置
DATA_SOURCE_PRIORITY = {
    "china_stocks": ["tdx", "yahoo"],      # A股优先使用通达信
    "hk_stocks": ["yahoo", "finnhub"],     # 港股优先使用Yahoo
    "us_stocks": ["finnhub", "yahoo"],     # 美股优先使用FinnHub
}
```

### 2. 缓存配置优化

项目已配置智能缓存系统：

```python
# 缓存时间设置（小时）
CACHE_TTL = {
    "realtime_data": 0.1,    # 实时数据6分钟
    "daily_data": 6,         # 日线数据6小时  
    "fundamental_data": 24,  # 基本面数据24小时
    "news_data": 1,          # 新闻数据1小时
}
```

### 3. API密钥配置

编辑 `.env` 文件添加API密钥：

```bash
# FinnHub API密钥（可选，用于增强国际市场数据）
FINNHUB_API_KEY=your_finnhub_api_key_here

# 阿里百炼API密钥（用于LLM分析）
DASHSCOPE_API_KEY=your_dashscope_api_key_here

# Google AI API密钥（可选）
GOOGLE_API_KEY=your_google_api_key_here
```

## 🚀 使用示例

### 1. 分析A股股票

```bash
# 在WSL中激活环境并运行
source ~/.bashrc
cd /mnt/e/AI/TradingAgents-CN
conda activate TACN

# 运行CLI分析
python cli/main.py
# 选择 "2" (A股)
# 输入股票代码，如 "600036"
```

### 2. 使用Web界面

```bash
# 启动Web界面
python web/run_web.py

# 或使用批处理脚本
./start_web.ps1
```

### 3. 编程接口使用

```python
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# 配置中文分析
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "dashscope"  # 使用阿里百炼
config["deep_think_llm"] = "qwen-plus"
config["quick_think_llm"] = "qwen-turbo"

# 创建分析实例
ta = TradingAgentsGraph(debug=True, config=config)

# 分析A股股票
state, decision = ta.propagate("600036", "2024-06-30")
print(decision)
```

## 🔍 故障排除

### 1. 通达信连接问题

如果通达信API连接失败：

```bash
# 测试网络连接
ping 115.238.56.198

# 重新测试连接
python test_datasources.py
```

### 2. 数据获取异常

```python
# 检查股票代码格式
# A股：6位数字（如 600036）
# 港股：4位数字.HK（如 0700.HK）
# 美股：字母代码（如 AAPL）
```

### 3. 缓存清理

```bash
# 清理数据缓存
rm -rf tradingagents/dataflows/data_cache/*
```

## 📈 支持的市场和股票类型

| 市场 | 数据源 | 支持程度 | 示例代码 |
|------|--------|----------|----------|
| **A股主板** | 通达信 | ✅ 完全支持 | 600036, 000001 |
| **深交所** | 通达信 | ✅ 完全支持 | 000858, 002415 |
| **创业板** | 通达信 | ✅ 完全支持 | 300015, 300750 |
| **科创板** | 通达信 | ✅ 完全支持 | 688981, 688111 |
| **港股** | Yahoo Finance | ✅ 完全支持 | 0700.HK, 9988.HK |
| **美股** | FinnHub/Yahoo | ✅ 完全支持 | AAPL, TSLA |

## 🎉 配置完成

您的TradingAgents-CN项目已经完成了中国证券市场数据源的配置！

**下一步建议：**
1. 🔑 获取FinnHub API密钥以增强数据覆盖
2. 🧠 配置阿里百炼API密钥以使用中文LLM
3. 🚀 开始使用CLI或Web界面进行股票分析
4. 📊 探索多智能体协作分析功能

**技术支持：**
- 📖 查看 `docs/` 目录下的详细文档
- 🧪 运行 `test_datasources.py` 验证配置
- 🌐 使用Web界面进行可视化分析
