#!/bin/bash
# TradingAgents-CN 快速启动脚本

echo "🚀 TradingAgents-CN 快速启动"
echo "================================"

# 激活conda环境
echo "📦 激活conda环境 TACN..."
source ~/.bashrc
conda activate TACN

# 检查环境
echo "🔍 检查Python环境..."
python --version

# 进入项目目录
cd /mnt/e/AI/TradingAgents-CN

echo ""
echo "🎯 请选择启动方式:"
echo "1) CLI命令行界面"
echo "2) Web网页界面"
echo "3) 测试数据源"
echo "4) 快速股票分析示例"
echo ""

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🖥️ 启动CLI界面..."
        python cli/main.py
        ;;
    2)
        echo "🌐 启动Web界面..."
        echo "💡 启动后请在浏览器中访问: http://localhost:8501"
        python web/run_web.py
        ;;
    3)
        echo "🔍 测试数据源连接..."
        python test_datasources.py
        ;;
    4)
        echo "📊 运行快速分析示例..."
        python test_china_stock_data.py
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
