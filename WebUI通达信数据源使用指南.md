# 🌐 TradingAgents-CN WebUI 通达信数据源使用指南

## 📊 现状分析

根据代码分析，通达信数据源已经完全集成到TradingAgents-CN项目中，但在WebUI中的使用需要正确的配置和操作方式。

## 🎯 WebUI中如何使用通达信数据源

### 1. 启动WebUI

```bash
# 在WSL中激活环境
source ~/.bashrc
cd /mnt/e/AI/TradingAgents-CN
conda activate TACN

# 启动Web界面
python web/run_web.py
```

### 2. 在WebUI中分析A股（自动使用通达信）

**关键步骤：**

1. **选择市场类型** - 在分析表单中选择 **"A股"**
2. **输入股票代码** - 输入6位A股代码（如：600036）
3. **配置分析师** - 选择需要的分析师
4. **开始分析** - 系统会自动使用通达信数据源

### 3. 数据源自动选择逻辑

项目已经内置了智能数据源选择机制：

```python
# 自动数据源选择逻辑
if market_type == "A股":
    # 自动使用通达信数据源
    - 市场分析师 → 通达信API获取K线和技术指标
    - 基本面分析师 → 通达信API获取基本面数据
    - 社交媒体分析师 → 中文财经数据聚合器
    
elif market_type == "美股":
    # 自动使用Yahoo Finance/FinnHub
    - 市场分析师 → Yahoo Finance API
    - 基本面分析师 → FinnHub API
    - 社交媒体分析师 → Reddit API
```

## 🔧 WebUI配置优化

### 1. 数据源配置页面

虽然WebUI目前没有专门的数据源配置页面，但您可以通过以下方式优化：

**创建数据源配置页面：**

```python
# 在 web/pages/ 目录下创建 data_source_config.py
def render_data_source_config():
    st.header("📊 数据源配置")
    
    # 通达信配置
    st.subheader("🇨🇳 通达信API配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        tdx_enabled = st.checkbox("启用通达信API", value=True)
        tdx_cache_ttl = st.slider("缓存时间(小时)", 1, 24, 6)
    
    with col2:
        tdx_server = st.selectbox(
            "服务器选择",
            ["自动选择", "**************:7709", "**************:7709"]
        )
        tdx_timeout = st.slider("连接超时(秒)", 5, 30, 10)
```

### 2. 当前WebUI使用方法

**步骤详解：**

1. **访问WebUI**
   - 启动后访问：http://localhost:8501
   - 界面会显示股票分析平台

2. **选择A股市场**
   - 在"选择市场"下拉框中选择 **"A股"**
   - 这会自动激活通达信数据源

3. **输入A股代码**
   - 输入6位数字代码，例如：
     - `600036` (招商银行)
     - `000001` (平安银行)
     - `000858` (五粮液)
     - `600519` (贵州茅台)

4. **选择分析师团队**
   - ✅ **市场分析师** - 使用通达信获取技术分析数据
   - ✅ **基本面分析师** - 使用通达信获取基本面数据
   - ⚠️ **社交媒体分析师** - 使用中文财经数据聚合器
   - ⚠️ **新闻分析师** - 使用中文新闻数据源

5. **配置研究深度**
   - 1级：快速分析（部分使用缓存）
   - 2-3级：标准分析（完全使用在线数据）
   - 4-5级：深度分析（多轮分析）

6. **开始分析**
   - 点击"开始分析"按钮
   - 系统会自动调用通达信API获取数据

## 📈 实际使用示例

### 示例1：分析招商银行

1. **市场选择**：A股
2. **股票代码**：600036
3. **分析师**：市场分析师 + 基本面分析师
4. **研究深度**：3级（标准分析）

**预期结果：**
- 通达信API获取实时价格：¥46.45
- 技术指标：MA5, MA10, RSI, MACD等
- 历史K线数据和趋势分析
- 基本面数据和财务指标

### 示例2：分析贵州茅台

1. **市场选择**：A股
2. **股票代码**：600519
3. **分析师**：全部分析师
4. **研究深度**：4级（深度分析）

**预期结果：**
- 完整的技术面分析（通达信数据）
- 详细的基本面分析
- 中文新闻情绪分析
- 多智能体协作决策

## 🔍 数据源状态监控

### 在WebUI中查看数据源状态

**方法1：通过日志输出**
```
📊 正在获取中国股票数据: 600036 (2025-06-28 到 2025-07-01)
✅ 通达信API连接成功: **************:7709
💾 股票数据已缓存: 600036 (tdx) -> 600036_stock_data_xxx
```

**方法2：通过分析结果**
- 查看分析报告底部的"数据来源"信息
- 确认显示"数据来源: 通达信API (实时数据)"

## ⚠️ 常见问题和解决方案

### 1. A股数据获取失败

**问题现象：**
```
❌ 中国股票数据获取失败 - 600036
```

**解决方案：**
```bash
# 测试通达信连接
python check_tdx_connection.py

# 清理缓存重试
rm -rf tradingagents/dataflows/data_cache/*
```

### 2. WebUI显示美股数据源

**问题现象：**
- 选择了A股但仍使用Yahoo Finance

**解决方案：**
- 确保市场类型选择为"A股"
- 检查股票代码格式（6位数字）
- 重启WebUI服务

### 3. 数据源切换不生效

**问题现象：**
- 修改配置后数据源未切换

**解决方案：**
```bash
# 重启WebUI
Ctrl+C  # 停止当前服务
python web/run_web.py  # 重新启动
```

## 🚀 高级配置

### 1. 自定义数据源优先级

编辑 `tradingagents/dataflows/config.py`：

```python
# 自定义数据源优先级
DATA_SOURCE_CONFIG = {
    "china_stocks": {
        "primary": "tdx",           # 主要使用通达信
        "fallback": ["yahoo"],      # 备用Yahoo Finance
        "cache_ttl": 6              # 缓存6小时
    }
}
```

### 2. WebUI数据源显示

在分析结果中添加数据源信息显示：

```python
# 在分析结果中显示使用的数据源
def format_analysis_results(decision, state, risk_assessment):
    # 添加数据源信息
    data_sources_used = extract_data_sources(state)
    
    formatted_result = f"""
    {decision}
    
    ---
    📊 **数据源信息**
    {data_sources_used}
    """
```

## 🎯 总结

**✅ 通达信数据源在WebUI中的使用非常简单：**

1. **选择"A股"市场** → 自动激活通达信数据源
2. **输入6位股票代码** → 自动识别为A股
3. **选择分析师** → 自动使用对应的数据获取工具
4. **开始分析** → 系统自动调用通达信API

**🔧 无需额外配置，开箱即用！**

通达信数据源已经完全集成到WebUI的分析流程中，当您选择分析A股时，系统会自动：
- 使用通达信API获取实时行情
- 获取历史K线数据
- 计算技术指标
- 提供中文化的分析结果

**现在就可以在WebUI中分析您感兴趣的A股股票了！** 🚀
