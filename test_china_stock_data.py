#!/usr/bin/env python3
"""
测试中国股票数据获取功能
"""

import sys
import os
from datetime import datetime, timedelta

def test_china_stock_data():
    """测试中国股票数据获取"""
    print("🔍 测试中国股票数据获取...")
    
    try:
        from tradingagents.dataflows.tdx_utils import get_china_stock_data
        
        # 测试招商银行数据
        stock_code = "600036"  # 招商银行
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        print(f"📊 获取股票数据: {stock_code}")
        print(f"📅 时间范围: {start_date} 到 {end_date}")
        
        data = get_china_stock_data(stock_code, start_date, end_date)
        
        if data and "❌" not in data:
            print("✅ 数据获取成功!")
            print("📈 数据预览:")
            print("-" * 50)
            # 只显示前500个字符
            preview = data[:500] + "..." if len(data) > 500 else data
            print(preview)
            print("-" * 50)
        else:
            print("❌ 数据获取失败")
            if data:
                print(data)
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_yahoo_finance_data():
    """测试Yahoo Finance数据获取"""
    print("\n🔍 测试Yahoo Finance数据获取...")
    
    try:
        from tradingagents.dataflows.interface import get_YFin_data_online
        
        # 测试腾讯控股港股数据
        symbol = "0700.HK"  # 腾讯控股
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        print(f"📊 获取港股数据: {symbol}")
        print(f"📅 时间范围: {start_date} 到 {end_date}")
        
        data = get_YFin_data_online(symbol, start_date, end_date)
        
        if data and "No data found" not in str(data):
            print("✅ 港股数据获取成功!")
            print("📈 数据预览:")
            print("-" * 50)
            # 只显示前500个字符
            preview = str(data)[:500] + "..." if len(str(data)) > 500 else str(data)
            print(preview)
            print("-" * 50)
        else:
            print("❌ 港股数据获取失败")
            print(data)
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_chinese_sentiment():
    """测试中文情绪分析"""
    print("\n🔍 测试中文情绪分析...")
    
    try:
        from tradingagents.dataflows.chinese_finance_utils import get_chinese_social_sentiment
        
        stock_code = "600036"  # 招商银行
        curr_date = datetime.now().strftime('%Y-%m-%d')
        
        print(f"📊 获取情绪分析: {stock_code}")
        print(f"📅 分析日期: {curr_date}")
        
        sentiment = get_chinese_social_sentiment(stock_code, curr_date)
        
        if sentiment:
            print("✅ 情绪分析获取成功!")
            print("📈 分析结果:")
            print("-" * 50)
            # 只显示前800个字符
            preview = sentiment[:800] + "..." if len(sentiment) > 800 else sentiment
            print(preview)
            print("-" * 50)
        else:
            print("❌ 情绪分析获取失败")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    print("=" * 60)
    print("🚀 TradingAgents-CN 中国股票数据获取测试")
    print("=" * 60)
    
    test_china_stock_data()
    test_yahoo_finance_data()
    test_chinese_sentiment()
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成")
    print("💡 提示: 如果数据获取成功，说明您的数据源配置正确!")
    print("🚀 现在可以开始使用TradingAgents进行股票分析了!")
    print("=" * 60)

if __name__ == "__main__":
    main()
