#!/usr/bin/env python3
"""
快速通达信连接和数据获取测试
"""

def quick_test():
    print("🚀 快速通达信测试")
    print("=" * 40)
    
    try:
        from tradingagents.dataflows.tdx_utils import get_china_stock_data
        from datetime import datetime, timedelta
        
        # 测试几个热门A股
        test_stocks = [
            ("000001", "平安银行"),
            ("600036", "招商银行"), 
            ("000858", "五粮液"),
            ("600519", "贵州茅台"),
            ("000002", "万科A")
        ]
        
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y-%m-%d')
        
        success_count = 0
        
        for stock_code, stock_name in test_stocks:
            try:
                print(f"📊 测试 {stock_code} ({stock_name})...")
                data = get_china_stock_data(stock_code, start_date, end_date)
                
                if data and "❌" not in data and "当前价格" in data:
                    print(f"✅ {stock_name} - 数据获取成功")
                    success_count += 1
                    
                    # 提取价格信息
                    lines = data.split('\n')
                    for line in lines:
                        if "当前价格" in line:
                            print(f"   {line.strip()}")
                            break
                else:
                    print(f"❌ {stock_name} - 数据获取失败")
                    
            except Exception as e:
                print(f"❌ {stock_name} - 异常: {e}")
        
        print("\n" + "=" * 40)
        print(f"📊 测试结果: {success_count}/{len(test_stocks)} 成功")
        
        if success_count == len(test_stocks):
            print("🎉 通达信数据源完全正常!")
        elif success_count > 0:
            print("⚠️ 通达信数据源部分正常")
        else:
            print("❌ 通达信数据源存在问题")
            
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    quick_test()
